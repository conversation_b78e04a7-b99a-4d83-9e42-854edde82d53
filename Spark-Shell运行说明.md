# Spark-Shell 运行说明

## 概述

本文档详细说明如何使用 spark-shell 运行大数据实时处理技术实训项目。所有程序已经修改为适合在 spark-shell 环境中直接运行。

## 环境准备

### 方法一：自动配置（推荐）

```bash
# 一键启动（推荐）
chmod +x start.sh
./start.sh

# 或者分步配置
chmod +x setup_environment.sh
./setup_environment.sh
source spark_env.sh
```

### 方法二：手动配置

如果自动配置失败，可以手动设置：

```bash
# 查找Java安装路径
find /usr/lib/jvm -name "java" -type f 2>/dev/null

# 查找Spark安装路径
find /opt -name "spark-shell" -type f 2>/dev/null

# 设置环境变量（根据实际路径修改）
export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk
export SPARK_HOME=/opt/spark
export PATH=$SPARK_HOME/bin:$JAVA_HOME/bin:$PATH
```

### 3. 验证环境

```bash
# 快速验证
./quick_test.sh

# 或手动验证
java -version
spark-shell --version
```

## 运行方式

### 方式一：使用自动化脚本（推荐）

#### 1. 一键启动（最简单）

```bash
chmod +x start.sh
./start.sh
```

这个脚本会：
- 自动检测和配置环境
- 提供多种运行选项
- 自动处理所有依赖

#### 2. 交互式运行（推荐新手）

```bash
chmod +x run_interactive.sh
./run_interactive.sh
```

这个脚本提供菜单选择，可以分步执行：
- 生成人口年龄数据
- 运行人口年龄分析
- 运行咖啡数据分析
- 查看分析结果

#### 3. 自动运行（有经验用户）

```bash
chmod +x run_spark_shell.sh
./run_spark_shell.sh
```

### 方式二：手动执行

#### 第一步：生成人口年龄数据

```bash
# 编译并运行数据生成程序
scalac GeneratePeopleAge.scala
scala GeneratePeopleAge 1000 peopleage.txt
```

#### 第二步：运行人口年龄分析

```bash
# 启动spark-shell并加载分析程序
spark-shell --master local[*] \
            --conf spark.ui.enabled=false \
            --conf spark.sql.warehouse.dir=/tmp/spark-warehouse \
            -i CalculateAverageAge.scala
```

**说明：**
- `-i CalculateAverageAge.scala`: 启动时自动加载并执行脚本
- `--master local[*]`: 使用本地模式，使用所有可用CPU核心
- `--conf spark.ui.enabled=false`: 禁用Web UI（可选）
- 程序会自动执行分析并生成 `age_analysis_results.txt` 文件

#### 第三步：运行咖啡数据分析

```bash
# 启动spark-shell并加载咖啡分析程序
spark-shell --master local[*] \
            --conf spark.ui.enabled=false \
            --conf spark.sql.warehouse.dir=/tmp/spark-warehouse \
            -i CoffeeChainAnalysis.scala \
            --conf spark.driver.memory=2g
```

**说明：**
- `--conf spark.driver.memory=2g`: 增加驱动程序内存（咖啡数据较大）
- 程序会自动执行分析并生成 `coffee_analysis_results.txt` 文件

## 程序功能说明

### 1. CalculateAverageAge.scala

**功能：** 计算人口平均年龄

**主要函数：**
```scala
def calculateAverageAge(inputFile: String = "peopleage.txt")
```

**执行流程：**
1. 读取 peopleage.txt 文件
2. 解析年龄数据
3. 计算统计信息（平均值、最大值、最小值）
4. 统计年龄分布
5. 输出结果到控制台和文件

**输出文件：** `age_analysis_results.txt`

### 2. CoffeeChainAnalysis.scala

**功能：** 咖啡连锁店多维度数据分析

**主要函数：**
```scala
def analyzeCoffeeData(inputFile: String = "CoffeeChain.csv")
```

**分析维度：**
1. 数据预处理和基本统计
2. 销售量排名分析
3. 销售量与州的关系
4. 销售量与市场的关系
5. 利润和售价分析
6. 利润、售价、销售量关系
7. 利润、销售量与成本关系
8. 产品属性分析
9. 市场规模和地域分析

**输出文件：** `coffee_analysis_results.txt`

## 预期输出

### 人口年龄分析结果示例

```
==================================================
人口年龄统计结果
==================================================
总人数：1,000 人
平均年龄：49.23 岁
最大年龄：80 岁
最小年龄：18 岁
年龄总和：49,230 岁

年龄分布：
------------------------------
18-19岁   :     32 人 ( 3.2%)
20-29岁   :    158 人 (15.8%)
30-39岁   :    162 人 (16.2%)
40-49岁   :    159 人 (15.9%)
50-59岁   :    174 人 (17.4%)
60-69岁   :    156 人 (15.6%)
70岁以上  :    159 人 (15.9%)
```

### 咖啡分析结果示例

```
============================================================
1. 数据预处理和基本统计信息
============================================================
总记录数: 4,248
涉及州数: 13
产品种类数: 13
市场数: 5
总销售额: $1,058,628.00
总利润: $383,371.00
平均销售额: $249.22
平均利润: $90.25

各州数据分布:
New York       :    816 条记录
California     :    816 条记录
Illinois       :    624 条记录
...
```

## 常见问题解决

### 1. 内存不足错误

**问题：** `java.lang.OutOfMemoryError`

**解决方案：**
```bash
spark-shell --master local[*] \
            --conf spark.driver.memory=2g \
            --conf spark.executor.memory=2g \
            -i YourScript.scala
```

### 2. 文件找不到错误

**问题：** `java.io.FileNotFoundException`

**解决方案：**
- 确保数据文件在当前目录
- 检查文件名是否正确
- 确保已生成 peopleage.txt 文件

### 3. 编译错误

**问题：** Scala编译失败

**解决方案：**
- 检查Scala版本兼容性
- 确保语法正确
- 重新下载源文件

### 4. Java路径错误

**问题：** `/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.412.b08-1.el7_9.x86_64/bin/java: 没有那个文件或目录`

**解决方案：**
```bash
# 运行环境配置脚本
./setup_environment.sh

# 或手动查找正确路径
find /usr/lib/jvm -name "java" -type f 2>/dev/null
ls -la /usr/lib/jvm/
```

### 5. Spark启动失败

**问题：** spark-shell无法启动

**解决方案：**
- 运行 `./setup_environment.sh` 自动配置
- 检查JAVA_HOME和SPARK_HOME环境变量
- 确保Java版本为1.8
- 检查端口是否被占用

### 6. 权限问题

**问题：** 脚本无法执行

**解决方案：**
```bash
chmod +x *.sh
```

## 性能优化建议

### 1. 内存配置

```bash
# 对于大数据集
spark-shell --master local[*] \
            --conf spark.driver.memory=4g \
            --conf spark.executor.memory=4g \
            --conf spark.driver.maxResultSize=2g
```

### 2. 并行度调整

```bash
# 根据CPU核心数调整
spark-shell --master local[4]  # 使用4个核心
```

### 3. 缓存策略

在程序中使用：
```scala
dataRDD.cache()  // 缓存频繁使用的RDD
```

## 验证结果

### 1. 检查输出文件

```bash
ls -la *.txt
```

应该看到：
- `peopleage.txt` - 人口年龄数据
- `age_analysis_results.txt` - 人口分析结果
- `coffee_analysis_results.txt` - 咖啡分析结果

### 2. 查看文件内容

```bash
# 查看人口分析结果
cat age_analysis_results.txt

# 查看咖啡分析结果（前50行）
head -50 coffee_analysis_results.txt
```

## 注意事项

1. **退出spark-shell**：分析完成后输入 `:quit` 退出
2. **文件权限**：确保脚本有执行权限 `chmod +x *.sh`
3. **磁盘空间**：确保有足够的磁盘空间存储结果文件
4. **网络连接**：首次运行可能需要下载依赖包

## 技术支持

如果遇到问题：
1. 检查环境配置是否正确
2. 查看错误日志信息
3. 确认所有必需文件都存在
4. 尝试重新启动spark-shell

---

**完成标志：** 成功生成两个结果文件并包含预期的分析结果
